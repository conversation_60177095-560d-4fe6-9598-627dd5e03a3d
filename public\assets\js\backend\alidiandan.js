
define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        // 公共授权流程函数
        checkAndAuth: function(account_identity, successCallback, context) {
            context = context || window;
            
            Fast.api.ajax({
                url: '/api/isv/checkAuthStatus',
                data: {account_identity: account_identity},
            }, function(ret) {
                var data = ret.data || ret;
                
                if (!data || !data.status) {
                    var Toastr = context.Toastr || window.Toastr;
                    Toastr.error('获取授权状态失败：返回数据格式异常');
                    return;
                }
                
                if (data.status === 'valid' || data.status === 'expiring_soon') {
                    // 已授权且有效
                    if (successCallback) successCallback();
                } else if (data.status === 'expired') {
                    // token已过期，提示刷新
                    var Layer = context.Layer || window.Layer;
                    Layer.confirm('该账号授权已过期，是否刷新授权？', function(index) {
                        Controller.refreshAuthToken(account_identity, successCallback, context);
                        Layer.close(index);
                    });
                } else {
                    // 未授权或其他状态，显示授权二维码
                    var authUrl = Controller.generateAuthUrl(account_identity, context);
                    var title = data.status === 'not_authorized' ? '请完成授权' : '请完成授权 - ' + (data.status || '未知状态');
                    Controller.showAuthQrcode(authUrl, title, context, account_identity, successCallback);
                }
            }, function(ret) {
                var Toastr = context.Toastr || window.Toastr;
                Toastr.error(ret.msg || '检查授权状态失败');
            });
        },

        // 安全的Base64编码函数，支持中文字符
        safeBase64Encode: function(str) {
            try {
                // 先将字符串转换为UTF-8字节序列，然后进行Base64编码
                return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(_, p1) {
                    return String.fromCharCode('0x' + p1);
                }));
            } catch (e) {
                // 如果编码失败，返回原字符串的URL编码
                return encodeURIComponent(str);
            }
        },

        // 生成授权链接
        generateAuthUrl: function(account_identity, context) {
            context = context || window;
            var appId = '****************';
            var redirectUri = 'http://*************:8866/api/isv/alipay_auth_callback';
            // 使用安全的Base64编码函数
            var state = Controller.safeBase64Encode(account_identity);
            
            var params = [];
            params.push('app_id=' + appId);
            params.push('redirect_uri=' + encodeURIComponent(redirectUri));
            params.push('state=' + state);
            
            return 'https://openauth.alipay.com/oauth2/appToAppAuth.htm?' + params.join('&');
        },

        // 显示授权二维码
        showAuthQrcode: function(authUrl, title, context, account_identity, successCallback) {
            context = context || window;
            title = title || '支付宝授权';

            // 使用前端生成二维码，不依赖外部接口
            var qrcodeHtml = '<div style="text-align:center;padding:20px;">' +
                             '<p>请使用支付宝扫描二维码完成授权：</p>' +
                             '<div style="margin:20px auto;">' +
                             '<div id="qrcode-container" style="display:inline-block;"></div>' +
                             '<div id="qrcode-fallback" style="display:none;padding:20px;">' +
                                 '<p style="color:red;margin-bottom:15px;">二维码生成失败</p>' +
                                 '<p><a href="' + authUrl + '" target="_blank" style="color:blue;text-decoration:underline;font-size:16px;">点击此链接进行授权</a></p>' +
                                 '<p style="font-size:12px;color:#666;margin-top:15px;word-break:break-all;">授权链接：<br>' + authUrl + '</p>' +
                             '</div>' +
                             '</div>' +
                             '<p id="auth-status" style="margin-top:10px;color:#666;"><small>等待授权中...</small></p>' +
                             '</div>';

            var Layer = context.Layer || window.Layer;
            var layerIndex = Layer.open({
                type: 1,
                title: title,
                area: ['450px', '550px'],
                content: qrcodeHtml,
                success: function() {
                    // 生成二维码
                    Controller.generateQrcode(authUrl, 'qrcode-container');
                    // 开始定时检查授权状态
                    Controller.startAuthStatusCheck(account_identity, layerIndex, successCallback, context);
                }
            });
        },

        // 前端生成二维码
        generateQrcode: function(text, containerId) {
            try {
                // 确保jQuery.qrcode库已加载
                if (typeof jQuery.fn.qrcode === 'undefined') {
                    // 如果库未加载，尝试动态加载
                    require(['qrcode'], function() {
                        Controller.createQrcode(text, containerId);
                    });
                } else {
                    Controller.createQrcode(text, containerId);
                }
            } catch (e) {
                console.error('生成二维码失败:', e);
                // 显示备用方案
                document.getElementById('qrcode-container').style.display = 'none';
                document.getElementById('qrcode-fallback').style.display = 'block';
            }
        },

        // 创建二维码
        createQrcode: function(text, containerId) {
            try {
                var container = document.getElementById(containerId);
                if (container) {
                    // 清空容器
                    container.innerHTML = '';
                    // 使用jQuery.qrcode生成二维码
                    $(container).qrcode({
                        text: text,
                        width: 256,
                        height: 256,
                        background: '#ffffff',
                        foreground: '#000000',
                        correctLevel: 2 // 错误纠正级别：L=1, M=0, Q=3, H=2
                    });
                }
            } catch (e) {
                console.error('创建二维码失败:', e);
                // 显示备用方案
                document.getElementById('qrcode-container').style.display = 'none';
                document.getElementById('qrcode-fallback').style.display = 'block';
            }
        },

        // 开始定时检查授权状态
        startAuthStatusCheck: function(account_identity, layerIndex, successCallback, context) {
            context = context || window;
            var checkCount = 0;
            var maxChecks = 100; // 最多检查100次（5分钟）
            
            var checkInterval = setInterval(function() {
                checkCount++;
                
                // 更新状态提示
                var statusEl = document.getElementById('auth-status');
                if (statusEl) {
                    statusEl.innerHTML = '<small>等待授权中... (' + checkCount + '/' + maxChecks + ')</small>';
                }
                
                // 检查授权状态
                Fast.api.ajax({
                    url: '/api/isv/checkAuthStatus',
                    data: {account_identity: account_identity},
                }, function(ret) {
                    var data = ret.data || ret;
                    
                    if (data && (data.status === 'valid' || data.status === 'expiring_soon')) {
                        // 授权成功
                        clearInterval(checkInterval);
                        
                        // 更新状态提示
                        if (statusEl) {
                            statusEl.innerHTML = '<small style="color:green;">授权成功！正在关闭窗口...</small>';
                        }
                        
                        var Toastr = context.Toastr || window.Toastr;
                        Toastr.success('授权成功！');
                        
                        // 延迟关闭窗口并执行回调
                        setTimeout(function() {
                            var Layer = context.Layer || window.Layer;
                            Layer.close(layerIndex);
                            
                            // 执行成功回调
                            if (successCallback) {
                                successCallback();
                            }
                        }, 1000);
                        
                    } else if (checkCount >= maxChecks) {
                        // 超时
                        clearInterval(checkInterval);
                        if (statusEl) {
                            statusEl.innerHTML = '<small style="color:red;">授权超时，请手动刷新页面检查</small>';
                        }
                    }
                }, function(ret) {
                    // 请求失败，继续检查
                    console.log('检查授权状态失败:', ret.msg);
                    
                    if (checkCount >= maxChecks) {
                        clearInterval(checkInterval);
                        var statusEl = document.getElementById('auth-status');
                        if (statusEl) {
                            statusEl.innerHTML = '<small style="color:red;">检查超时，请手动刷新页面</small>';
                        }
                    }
                });
                
            }, 3000); // 每3秒检查一次
        },

        // 刷新授权token
        refreshAuthToken: function(account_identity, callback, context) {
            context = context || window;
            Fast.api.ajax({
                url: '/api/isv/refreshAppAuthToken',
                data: {account_identity: account_identity},
            }, function(ret) {
                var Toastr = context.Toastr || window.Toastr;
                Toastr.success('授权刷新成功');
                if (callback) callback();
            }, function(ret) {
                var Toastr = context.Toastr || window.Toastr;
                Toastr.error(ret.msg || '刷新授权失败');
            });
        },

        // 自定义状态格式化器
        customToggleFormatter: function(value, row, index) {
            var domId = 'switch-' + row.id;
            var switchClass = value === 'normal' ? '' : 'fa-flip-horizontal text-gray';
            
            return '<input id="' + domId + '" name="status-' + row.id + '" type="hidden" value="' + value + '">' +
                   '<a href="javascript:;" data-toggle="switcher" class="btn-switcher custom-switch" ' +
                   'data-input-id="' + domId + '" data-yes="normal" data-no="hidden" ' +
                   'data-id="' + row.id + '" data-account="' + row.account_identity + '">' +
                   '<i class="fa fa-toggle-on text-success ' + switchClass + ' fa-2x"></i></a>';
        },

        index: function () {
            $(".btn-edit").data("area", ["80%", "80%"]);
            $(".btn-add").data("area", ["80%", "80%"]);
            $(".btn-paylimit").data("area", ["80%", "80%"]);

            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'alidiandan/index' + location.search,
                    add_url: 'alidiandan/add',
                    edit_url: 'alidiandan/edit',
                    del_url: 'alidiandan/del',
                    paylimit_url: 'alidiandan/paylimit',
                    multi_url: 'alidiandan/multi',
                    import_url: 'alidiandan/import',
                    table: 'alidiandan',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('店铺名称')},
                        {field: 'account_identity', title: __('支付宝账号')},
                        {field: 'today_rate', title: __('今日成率%'), formatter: function (v) {
                                return "<span class='btn btn-success-light'>"+v+"</span>";
                            }},
                        {field: 'today', title: __('今日充值'),formatter: function (v) {
                                return "<span class='btn btn-success'>"+v+"</span>";
                            }},
                        {field: 'yesterday', title: __('昨日充值')},
                        {field: 'beforeday', title: __('前日充值')},
                        {field: 'min_price', title: __('最小金额')},
                        {field: 'max_price', title: __('最大金额')},
                        {field: 'maximum', title: __('并发限制')},
                        {field: 'pay_limit', title: __('收款笔数限制')},
                        {field: 'channel', title: __('通道编码'),formatter:Table.api.formatter.flag,custom:{'': 'info'}},
                        {field: 'status', title: __('状态'), searchList: {"normal":__('正常'),"hidden":__('禁用')}, formatter: Controller.customToggleFormatter},
                        {field: 'notes', title: __('备注')},
                        {field: 'statusinfo', title: __('异常状态')},
                        {field: 'admin.nickname', title: __('创建人'), formatter: Table.api.formatter.search},
                        {field: 'pulltime', title: __('拉单时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons :[
                                {
                                    name: 'test',
                                    text: '测试拉单',
                                    extend:'data-area=["400px","300px"]',
                                    classname: 'btn btn-xs btn-info btn-click',
                                    click: function (data,ret) {
                                        Fast.api.layer.open({
                                            type:2,
                                            title:'测试拉单',
                                            area:["350px","370px"],
                                            content:"alidiandan/test?ids="+ret.ids
                                        });
                                    }
                                },
                                // {
                                //     name: 'accountlog',
                                //     text: '提取账单',
                                //     extend:'data-area=["400px","300px"]',
                                //     icon: 'fa fa-file-powerpoint-o',
                                //     classname: 'btn btn-xs btn-info btn-click',
                                //     click: function (data,ret) {
                                //         Fast.api.layer.open({
                                //             type:2,
                                //             title:'提取账单',
                                //             area:["800px","370px"],
                                //             content:"alidiandan/accountlog?ids="+ret.ids
                                //         });
                                //     }
                                // }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 批量设置收款笔数
            $(".btn-paylimit").on('click', function (){
                var temp=Table.api.selectedids($("#table"));
                Fast.api.open($.fn.bootstrapTable.defaults.extend.paylimit_url+'?ids='+temp,'批量设置收款笔数');
            });

            // 绑定自定义switch开关事件
            $('#table').on('click.custom-switch', '.custom-switch', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var $this = $(this);
                var id = $this.data('id');
                var account_identity = $this.data('account');
                var inputId = $this.data('input-id');
                var $input = $('#' + inputId);
                var currentValue = $input.val();
                var newValue = currentValue === 'normal' ? 'hidden' : 'normal';
                
                // 如果是要开启状态，先检查授权
                if (newValue === 'normal') {
                    Controller.checkAndAuth(account_identity, function() {
                        Controller.toggleStatusWithSwitch($this, $input, id, newValue, table);
                    });
                } else {
                    // 直接关闭，不需要检查授权
                    Controller.toggleStatusWithSwitch($this, $input, id, newValue, table);
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        // 手动切换状态 - 适配switch开关
        toggleStatusWithSwitch: function($switch, $input, id, status, table) {
            Fast.api.ajax({
                url: 'alidiandan/multi',
                data: {
                    action: '',
                    ids: id,
                    params: 'status=' + status
                },
            }, function(data, ret) {
                // 更新隐藏input的值
                $input.val(status);
                
                // 更新switch开关的视觉状态
                var $icon = $switch.find('i');
                if (status === 'normal') {
                    $icon.removeClass('fa-flip-horizontal text-gray');
                } else {
                    $icon.addClass('fa-flip-horizontal text-gray');
                }
                
                Toastr.success('状态更新成功');
                if (table) {
                    table.bootstrapTable('refresh');
                }
            }, function(ret) {
                Toastr.error(ret.msg || '状态更新失败');
            });
        },
        
        add: function () {
            Controller.api.bindevent();
        },
        
        edit: function () {
            Controller.api.bindevent();
        },
        
        paylimit: function () {
            Controller.api.bindevent();
        },
        
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    
    return Controller;
});






